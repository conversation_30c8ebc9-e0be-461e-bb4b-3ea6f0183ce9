//@version=5

indicator("ICT AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500, dynamic_requests=true)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
show_only_intraday = input.bool(false, "Show Only on Intraday Timeframes", group=group_settings)
timezone = input.string("America/New_York", "Time Zone",['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], group=group_settings)
max_periods = input.int(3, "Periods to Display", minval=1, maxval=30, tooltip="Number of months to display", group=group_settings)

// Time Settings
group_time = "Time Settings"
month_start_day = input.int(1, "Month Start Day", minval=1, maxval=31, tooltip="Day of month to start monthly calculations", group=group_time)

// Monthly AMR Settings
group_amr = "Monthly AMR Settings"
show_monthly_levels = input.bool(true, "Show Monthly Levels", group=group_amr)
amr_months = input.int(3, "Months to Average", minval=1, maxval=24, group=group_amr)
show_full_amr = input.bool(true, "Show Full AMR", group=group_amr)
show_one_third_amr = input.bool(true, "Show 1/3 AMR", group=group_amr)
show_two_third_amr = input.bool(false, "Show 2/3 AMR", group=group_amr)
show_half_amr = input.bool(false, "Show 1/2 AMR", group=group_amr)

// Monthly AMR Multiplier Settings
group_amr_mult = "Monthly AMR Multiplier Settings"
show_amr_mult_1_5 = input.bool(false, "Show 1.5x AMR", group=group_amr_mult)
show_amr_mult_2_0 = input.bool(false, "Show 2.0x AMR", group=group_amr_mult)

// Monthly Fibonacci Extension Settings
group_amr_fib = "Monthly Fibonacci Extension Settings"
show_amr_fib_1_272 = input.bool(false, "Show 127.2% AMR", group=group_amr_fib)
show_amr_fib_1_618 = input.bool(false, "Show 161.8% AMR", group=group_amr_fib)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Month Open", group=group_line)

// Color Settings
group_color_monthly = "Monthly Color Settings"
true_month_open_color = input.color(color.white, "True Month Open Line", group=group_color_monthly)
full_amr_color = input.color(color.white, "Full AMR", group=group_color_monthly)
one_third_amr_color = input.color(color.white, "1/3 AMR", group=group_color_monthly)
two_third_amr_color = input.color(color.white, "2/3 AMR", group=group_color_monthly)
half_amr_color = input.color(color.white, "1/2 AMR", group=group_color_monthly)
amr_mult_1_5_color = input.color(color.white, "1.5x AMR", group=group_color_monthly)
amr_mult_2_0_color = input.color(color.white, "2.0x AMR", group=group_color_monthly)
amr_fib_1_272_color = input.color(color.white, "127.2% AMR", group=group_color_monthly)
amr_fib_1_618_color = input.color(color.white, "161.8% AMR", group=group_color_monthly)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
merge_overlapping_labels = input.bool(true, "Merge Overlapping Labels", tooltip="Combine labels when price levels are very close to each other", group=group_label)
overlap_threshold = input.float(0.1, "Overlap Threshold %", minval=0.01, maxval=1.0, step=0.01, tooltip="% of ATR to consider levels as overlapping", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === TIME LOGIC ===
// Monthly time logic
is_first_day_of_month = dayofmonth == month_start_day
month_timestamp = timestamp(timezone, year, month, dayofmonth, 0, 0)
start_of_month = month_timestamp and is_first_day_of_month
is_new_month = ta.change(time("M")) or (barstate.isfirst and show_monthly_levels)
in_current_month = true  // We'll always be in the current month, just need to track when a new month starts

// True Month Open logic (ICT methodology - 00:00 UTC on first day of month)
// We'll detect the first day of month using dayofmonth == 1
is_true_month_start = (dayofmonth == 1) and (hour == 0) and (minute == 0)

// Function to calculate the Average Monthly Range
calculate_amr(lookback_period) =>
    // Request monthly high-low range data
    mh = request.security(syminfo.tickerid, "M", high, barmerge.gaps_off, barmerge.lookahead_off)
    ml = request.security(syminfo.tickerid, "M", low, barmerge.gaps_off, barmerge.lookahead_off)
    mrange = mh - ml

    // Calculate the average of the monthly ranges
    ta.sma(mrange, lookback_period)

// === STATE VARIABLES ===
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// === MONTHLY VARIABLES ===
// True Month Open variables
var line true_month_open_line = na
var label true_month_open_label = na
var line true_month_vertical = na
var float true_month_open_price = na
var bool true_month_line_active = false

// AMR level lines
var line full_amr_up_line = na
var line full_amr_down_line = na
var line one_third_amr_up_line = na
var line one_third_amr_down_line = na
var line two_third_amr_up_line = na
var line two_third_amr_down_line = na
var line half_amr_up_line = na
var line half_amr_down_line = na

// AMR Multiplier level lines
var line amr_mult_1_5_up_line = na
var line amr_mult_1_5_down_line = na
var line amr_mult_2_0_up_line = na
var line amr_mult_2_0_down_line = na

// AMR Fibonacci Extension level lines
var line amr_fib_1_272_up_line = na
var line amr_fib_1_272_down_line = na
var line amr_fib_1_618_up_line = na
var line amr_fib_1_618_down_line = na

// AMR level labels
var label full_amr_up_label = na
var label full_amr_down_label = na
var label one_third_amr_up_label = na
var label one_third_amr_down_label = na
var label two_third_amr_up_label = na
var label two_third_amr_down_label = na
var label half_amr_up_label = na
var label half_amr_down_label = na

// AMR Multiplier level labels
var label amr_mult_1_5_up_label = na
var label amr_mult_1_5_down_label = na
var label amr_mult_2_0_up_label = na
var label amr_mult_2_0_down_label = na

// AMR Fibonacci Extension level labels
var label amr_fib_1_272_up_label = na
var label amr_fib_1_272_down_label = na
var label amr_fib_1_618_up_label = na
var label amr_fib_1_618_down_label = na

// AMR level values
var float monthly_range_val = na
var float full_amr_up = na
var float full_amr_down = na
var float one_third_amr_up = na
var float one_third_amr_down = na
var float two_third_amr_up = na
var float two_third_amr_down = na
var float half_amr_up = na
var float half_amr_down = na

// AMR Multiplier level values
var float amr_mult_1_5_up = na
var float amr_mult_1_5_down = na
var float amr_mult_2_0_up = na
var float amr_mult_2_0_down = na

// AMR Fibonacci Extension level values
var float amr_fib_1_272_up = na
var float amr_fib_1_272_down = na
var float amr_fib_1_618_up = na
var float amr_fib_1_618_down = na

// === HISTORY ARRAYS ===
// Arrays to store historical range values
var float[] monthly_range_history = array.new_float()

// === MONTHLY ARRAYS ===
// Arrays to store historical monthly lines by type
var line[] tmo_lines = array.new_line()
var line[] full_amr_up_lines = array.new_line()
var line[] full_amr_down_lines = array.new_line()
var line[] one_third_amr_up_lines = array.new_line()
var line[] one_third_amr_down_lines = array.new_line()
var line[] two_third_amr_up_lines = array.new_line()
var line[] two_third_amr_down_lines = array.new_line()
var line[] half_amr_up_lines = array.new_line()
var line[] half_amr_down_lines = array.new_line()
var line[] monthly_vertical_lines = array.new_line()

// AMR Multiplier line arrays
var line[] amr_mult_1_5_up_lines = array.new_line()
var line[] amr_mult_1_5_down_lines = array.new_line()
var line[] amr_mult_2_0_up_lines = array.new_line()
var line[] amr_mult_2_0_down_lines = array.new_line()

// AMR Fibonacci Extension line arrays
var line[] amr_fib_1_272_up_lines = array.new_line()
var line[] amr_fib_1_272_down_lines = array.new_line()
var line[] amr_fib_1_618_up_lines = array.new_line()
var line[] amr_fib_1_618_down_lines = array.new_line()

// Arrays to store historical monthly labels by type
var label[] tmo_labels = array.new_label()
var label[] full_amr_up_labels = array.new_label()
var label[] full_amr_down_labels = array.new_label()
var label[] one_third_amr_up_labels = array.new_label()
var label[] one_third_amr_down_labels = array.new_label()
var label[] two_third_amr_up_labels = array.new_label()
var label[] two_third_amr_down_labels = array.new_label()
var label[] half_amr_up_labels = array.new_label()
var label[] half_amr_down_labels = array.new_label()

// AMR Multiplier label arrays
var label[] amr_mult_1_5_up_labels = array.new_label()
var label[] amr_mult_1_5_down_labels = array.new_label()
var label[] amr_mult_2_0_up_labels = array.new_label()
var label[] amr_mult_2_0_down_labels = array.new_label()

// AMR Fibonacci Extension label arrays
var label[] amr_fib_1_272_up_labels = array.new_label()
var label[] amr_fib_1_272_down_labels = array.new_label()
var label[] amr_fib_1_618_up_labels = array.new_label()
var label[] amr_fib_1_618_down_labels = array.new_label()

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to check if two price levels are close enough to be considered overlapping
is_overlapping(price1, price2, threshold) =>
    // Calculate the average true range (ATR) to determine a dynamic threshold
    atr_value = ta.atr(14)
    // Check if the difference between the two prices is less than the threshold percentage of ATR
    math.abs(price1 - price2) <= (atr_value * threshold)

// Function to merge label texts
merge_label_texts(text1, text2) =>
    // Extract price values if they exist
    price_start1 = str.pos(text1, "(")
    price_start2 = str.pos(text2, "(")

    // If both texts have price values, keep them separate
    if price_start1 > 0 and price_start2 > 0
        price_end1 = str.pos(text1, ")")
        price_end2 = str.pos(text2, ")")

        label1 = str.substring(text1, 0, price_start1 - 1)
        label2 = str.substring(text2, 0, price_start2 - 1)

        price1 = str.substring(text1, price_start1, price_end1)
        price2 = str.substring(text2, price_start2, price_end2)

        label1 + " + " + label2 + " " + price1 + "/" + price2 + ")"
    else
        // If no price values, just combine the labels
        text1 + " + " + text2

// Function to find and merge overlapping labels
find_overlapping_label(price, label_text, existing_prices, existing_texts, threshold) =>
    result_index = -1
    result_text = label_text

    if array.size(existing_prices) > 0
        for i = 0 to array.size(existing_prices) - 1
            existing_price = array.get(existing_prices, i)
            if is_overlapping(price, existing_price, threshold)
                result_index = i
                result_text := merge_label_texts(array.get(existing_texts, i), label_text)
                break

    [result_index, result_text]

// === MAIN LOGIC ===
// Calculate range values
float current_amr = calculate_amr(amr_months)

// Arrays to store price levels and label texts for merging overlapping labels
var float[] monthly_price_levels = array.new_float()
var string[] monthly_label_texts = array.new_string()

// Special initialization for monthly levels on the first bar
if barstate.isfirst and show_monthly_levels and not true_month_line_active
    monthly_range_val := current_amr
    true_month_open_price := close
    true_month_line_active := true

    // Calculate base AMR levels - these are always calculated regardless of multiplier/fib settings
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Calculate AMR Multiplier levels - only if enabled
    if show_amr_mult_1_5
        amr_mult_1_5_up := true_month_open_price + (monthly_range_val * 1.5)
        amr_mult_1_5_down := true_month_open_price - (monthly_range_val * 1.5)

    if show_amr_mult_2_0
        amr_mult_2_0_up := true_month_open_price + (monthly_range_val * 2.0)
        amr_mult_2_0_down := true_month_open_price - (monthly_range_val * 2.0)

    // Calculate AMR Fibonacci Extension levels - only if enabled
    if show_amr_fib_1_272
        amr_fib_1_272_up := true_month_open_price + (monthly_range_val * 1.272)
        amr_fib_1_272_down := true_month_open_price - (monthly_range_val * 1.272)

    if show_amr_fib_1_618
        amr_fib_1_618_up := true_month_open_price + (monthly_range_val * 1.618)
        amr_fib_1_618_down := true_month_open_price - (monthly_range_val * 1.618)

    // Create true month open line
    true_month_open_line := line.new(bar_index, true_month_open_price, bar_index, true_month_open_price, color=true_month_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tmo_lines, true_month_open_line)

    // Create true month open label
    if show_labels
        true_month_open_label := label.new(bar_index + label_x_offset_bars, true_month_open_price + label_y_offset, "TMO" + (show_price_in_label ? str.format(" ({0})", true_month_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_month_open_color, color=color.new(color.black, 100))
        array.push(tmo_labels, true_month_open_label)

    // Create AMR level lines
    if show_full_amr
        // Full AMR up line
        full_amr_up_line := line.new(bar_index, full_amr_up, bar_index, full_amr_up, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_up_lines, full_amr_up_line)

        // Full AMR up label
        if show_labels
            full_amr_up_label := label.new(bar_index + label_x_offset_bars, full_amr_up + label_y_offset, "AMR+" + (show_price_in_label ? str.format(" ({0})", full_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_up_labels, full_amr_up_label)

        // Full AMR down line
        full_amr_down_line := line.new(bar_index, full_amr_down, bar_index, full_amr_down, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_down_lines, full_amr_down_line)

        // Full AMR down label
        if show_labels
            full_amr_down_label := label.new(bar_index + label_x_offset_bars, full_amr_down + label_y_offset, "AMR-" + (show_price_in_label ? str.format(" ({0})", full_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_down_labels, full_amr_down_label)

    if show_one_third_amr
        // 1/3 AMR up line
        one_third_amr_up_line := line.new(bar_index, one_third_amr_up, bar_index, one_third_amr_up, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_up_lines, one_third_amr_up_line)

        // 1/3 AMR up label
        if show_labels
            one_third_amr_up_label := label.new(bar_index + label_x_offset_bars, one_third_amr_up + label_y_offset, "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", one_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_up_labels, one_third_amr_up_label)

        // 1/3 AMR down line
        one_third_amr_down_line := line.new(bar_index, one_third_amr_down, bar_index, one_third_amr_down, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_down_lines, one_third_amr_down_line)

        // 1/3 AMR down label
        if show_labels
            one_third_amr_down_label := label.new(bar_index + label_x_offset_bars, one_third_amr_down + label_y_offset, "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", one_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_down_labels, one_third_amr_down_label)

    if show_two_third_amr
        // 2/3 AMR up line
        two_third_amr_up_line := line.new(bar_index, two_third_amr_up, bar_index, two_third_amr_up, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_up_lines, two_third_amr_up_line)

        // 2/3 AMR up label
        if show_labels
            two_third_amr_up_label := label.new(bar_index + label_x_offset_bars, two_third_amr_up + label_y_offset, "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", two_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_up_labels, two_third_amr_up_label)

        // 2/3 AMR down line
        two_third_amr_down_line := line.new(bar_index, two_third_amr_down, bar_index, two_third_amr_down, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_down_lines, two_third_amr_down_line)

        // 2/3 AMR down label
        if show_labels
            two_third_amr_down_label := label.new(bar_index + label_x_offset_bars, two_third_amr_down + label_y_offset, "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", two_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_down_labels, two_third_amr_down_label)

    if show_half_amr
        // 1/2 AMR up line
        half_amr_up_line := line.new(bar_index, half_amr_up, bar_index, half_amr_up, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_up_lines, half_amr_up_line)

        // 1/2 AMR up label
        if show_labels
            half_amr_up_label := label.new(bar_index + label_x_offset_bars, half_amr_up + label_y_offset, "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", half_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_up_labels, half_amr_up_label)

        // 1/2 AMR down line
        half_amr_down_line := line.new(bar_index, half_amr_down, bar_index, half_amr_down, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_down_lines, half_amr_down_line)

        // 1/2 AMR down label
        if show_labels
            half_amr_down_label := label.new(bar_index + label_x_offset_bars, half_amr_down + label_y_offset, "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", half_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_down_labels, half_amr_down_label)

    // Create multiplier and Fibonacci extension levels
    if show_amr_mult_1_5
        amr_mult_1_5_up_line := line.new(bar_index, amr_mult_1_5_up, bar_index, amr_mult_1_5_up, color=amr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_1_5_up_lines, amr_mult_1_5_up_line)

        amr_mult_1_5_down_line := line.new(bar_index, amr_mult_1_5_down, bar_index, amr_mult_1_5_down, color=amr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_1_5_down_lines, amr_mult_1_5_down_line)

        if show_labels
            amr_mult_1_5_up_label := label.new(bar_index + label_x_offset_bars, amr_mult_1_5_up + label_y_offset, "1.5x AMR+" + (show_price_in_label ? str.format(" ({0})", amr_mult_1_5_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(amr_mult_1_5_up_labels, amr_mult_1_5_up_label)

            amr_mult_1_5_down_label := label.new(bar_index + label_x_offset_bars, amr_mult_1_5_down + label_y_offset, "1.5x AMR-" + (show_price_in_label ? str.format(" ({0})", amr_mult_1_5_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(amr_mult_1_5_down_labels, amr_mult_1_5_down_label)

    if show_amr_mult_2_0
        amr_mult_2_0_up_line := line.new(bar_index, amr_mult_2_0_up, bar_index, amr_mult_2_0_up, color=amr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_2_0_up_lines, amr_mult_2_0_up_line)

        amr_mult_2_0_down_line := line.new(bar_index, amr_mult_2_0_down, bar_index, amr_mult_2_0_down, color=amr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_2_0_down_lines, amr_mult_2_0_down_line)

        if show_labels
            amr_mult_2_0_up_label := label.new(bar_index + label_x_offset_bars, amr_mult_2_0_up + label_y_offset, "2.0x AMR+" + (show_price_in_label ? str.format(" ({0})", amr_mult_2_0_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(amr_mult_2_0_up_labels, amr_mult_2_0_up_label)

            amr_mult_2_0_down_label := label.new(bar_index + label_x_offset_bars, amr_mult_2_0_down + label_y_offset, "2.0x AMR-" + (show_price_in_label ? str.format(" ({0})", amr_mult_2_0_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(amr_mult_2_0_down_labels, amr_mult_2_0_down_label)

    if show_amr_fib_1_272
        amr_fib_1_272_up_line := line.new(bar_index, amr_fib_1_272_up, bar_index, amr_fib_1_272_up, color=amr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_272_up_lines, amr_fib_1_272_up_line)

        amr_fib_1_272_down_line := line.new(bar_index, amr_fib_1_272_down, bar_index, amr_fib_1_272_down, color=amr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_272_down_lines, amr_fib_1_272_down_line)

        if show_labels
            amr_fib_1_272_up_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_272_up + label_y_offset, "127.2% AMR+" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_272_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_272_up_labels, amr_fib_1_272_up_label)

            amr_fib_1_272_down_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_272_down + label_y_offset, "127.2% AMR-" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_272_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_272_down_labels, amr_fib_1_272_down_label)

    if show_amr_fib_1_618
        amr_fib_1_618_up_line := line.new(bar_index, amr_fib_1_618_up, bar_index, amr_fib_1_618_up, color=amr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_618_up_lines, amr_fib_1_618_up_line)

        amr_fib_1_618_down_line := line.new(bar_index, amr_fib_1_618_down, bar_index, amr_fib_1_618_down, color=amr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_618_down_lines, amr_fib_1_618_down_line)

        if show_labels
            amr_fib_1_618_up_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_618_up + label_y_offset, "161.8% AMR+" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_618_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_618_up_labels, amr_fib_1_618_up_label)

            amr_fib_1_618_down_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_618_down + label_y_offset, "161.8% AMR-" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_618_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_618_down_labels, amr_fib_1_618_down_label)

// Check if we're at the start of a new month or true month open
if (is_new_month or (is_true_month_start and not is_true_month_start[1])) and show_monthly_levels
    // Store the current AMR value in history and for this month's use
    array.unshift(monthly_range_history, current_amr)
    if array.size(monthly_range_history) > amr_months
        array.pop(monthly_range_history)

    // Update the monthly range value for this month
    monthly_range_val := current_amr

    // Update true month open price - using the price at the true month open time (ICT methodology)
    if is_true_month_start and not is_true_month_start[1]
        true_month_open_price := open
        true_month_line_active := true

    // Calculate base AMR levels - these are always calculated regardless of multiplier/fib settings
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Calculate AMR Multiplier levels - only if enabled
    if show_amr_mult_1_5
        amr_mult_1_5_up := true_month_open_price + (monthly_range_val * 1.5)
        amr_mult_1_5_down := true_month_open_price - (monthly_range_val * 1.5)

    if show_amr_mult_2_0
        amr_mult_2_0_up := true_month_open_price + (monthly_range_val * 2.0)
        amr_mult_2_0_down := true_month_open_price - (monthly_range_val * 2.0)

    // Calculate AMR Fibonacci Extension levels - only if enabled
    if show_amr_fib_1_272
        amr_fib_1_272_up := true_month_open_price + (monthly_range_val * 1.272)
        amr_fib_1_272_down := true_month_open_price - (monthly_range_val * 1.272)

    if show_amr_fib_1_618
        amr_fib_1_618_up := true_month_open_price + (monthly_range_val * 1.618)
        amr_fib_1_618_down := true_month_open_price - (monthly_range_val * 1.618)

// Update lines during the current month
if true_month_line_active and show_monthly_levels
    // Update true month open line
    line.set_x2(true_month_open_line, bar_index)
    line.set_y2(true_month_open_line, true_month_open_price)

    // Update true month open label
    if show_labels
        label.set_x(true_month_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_month_open_label, true_month_open_price + label_y_offset)

    // Update AMR level lines
    if show_full_amr
        line.set_x2(full_amr_up_line, bar_index)
        line.set_y2(full_amr_up_line, full_amr_up)
        line.set_x2(full_amr_down_line, bar_index)
        line.set_y2(full_amr_down_line, full_amr_down)

        if show_labels
            label.set_x(full_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_amr_up_label, full_amr_up + label_y_offset)
            label.set_x(full_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_amr_down_label, full_amr_down + label_y_offset)

    if show_one_third_amr
        line.set_x2(one_third_amr_up_line, bar_index)
        line.set_y2(one_third_amr_up_line, one_third_amr_up)
        line.set_x2(one_third_amr_down_line, bar_index)
        line.set_y2(one_third_amr_down_line, one_third_amr_down)

        if show_labels
            label.set_x(one_third_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_amr_up_label, one_third_amr_up + label_y_offset)
            label.set_x(one_third_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_amr_down_label, one_third_amr_down + label_y_offset)

    if show_two_third_amr
        line.set_x2(two_third_amr_up_line, bar_index)
        line.set_y2(two_third_amr_up_line, two_third_amr_up)
        line.set_x2(two_third_amr_down_line, bar_index)
        line.set_y2(two_third_amr_down_line, two_third_amr_down)

        if show_labels
            label.set_x(two_third_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_amr_up_label, two_third_amr_up + label_y_offset)
            label.set_x(two_third_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_amr_down_label, two_third_amr_down + label_y_offset)

    if show_half_amr
        line.set_x2(half_amr_up_line, bar_index)
        line.set_y2(half_amr_up_line, half_amr_up)
        line.set_x2(half_amr_down_line, bar_index)
        line.set_y2(half_amr_down_line, half_amr_down)

        if show_labels
            label.set_x(half_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_amr_up_label, half_amr_up + label_y_offset)
            label.set_x(half_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_amr_down_label, half_amr_down + label_y_offset)

    // Update AMR Multiplier lines - only if enabled
    if show_amr_mult_1_5
        // Recalculate the levels to ensure they're up to date
        amr_mult_1_5_up := true_month_open_price + (monthly_range_val * 1.5)
        amr_mult_1_5_down := true_month_open_price - (monthly_range_val * 1.5)

        line.set_x2(amr_mult_1_5_up_line, bar_index)
        line.set_y2(amr_mult_1_5_up_line, amr_mult_1_5_up)
        line.set_x2(amr_mult_1_5_down_line, bar_index)
        line.set_y2(amr_mult_1_5_down_line, amr_mult_1_5_down)

        if show_labels
            label.set_x(amr_mult_1_5_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_1_5_up_label, amr_mult_1_5_up + label_y_offset)
            label.set_x(amr_mult_1_5_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_1_5_down_label, amr_mult_1_5_down + label_y_offset)

    if show_amr_mult_2_0
        // Recalculate the levels to ensure they're up to date
        amr_mult_2_0_up := true_month_open_price + (monthly_range_val * 2.0)
        amr_mult_2_0_down := true_month_open_price - (monthly_range_val * 2.0)

        line.set_x2(amr_mult_2_0_up_line, bar_index)
        line.set_y2(amr_mult_2_0_up_line, amr_mult_2_0_up)
        line.set_x2(amr_mult_2_0_down_line, bar_index)
        line.set_y2(amr_mult_2_0_down_line, amr_mult_2_0_down)

        if show_labels
            label.set_x(amr_mult_2_0_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_2_0_up_label, amr_mult_2_0_up + label_y_offset)
            label.set_x(amr_mult_2_0_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_2_0_down_label, amr_mult_2_0_down + label_y_offset)

    // Update AMR Fibonacci Extension lines - only if enabled
    if show_amr_fib_1_272
        // Recalculate the levels to ensure they're up to date
        amr_fib_1_272_up := true_month_open_price + (monthly_range_val * 1.272)
        amr_fib_1_272_down := true_month_open_price - (monthly_range_val * 1.272)

        line.set_x2(amr_fib_1_272_up_line, bar_index)
        line.set_y2(amr_fib_1_272_up_line, amr_fib_1_272_up)
        line.set_x2(amr_fib_1_272_down_line, bar_index)
        line.set_y2(amr_fib_1_272_down_line, amr_fib_1_272_down)

        if show_labels
            label.set_x(amr_fib_1_272_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_272_up_label, amr_fib_1_272_up + label_y_offset)
            label.set_x(amr_fib_1_272_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_272_down_label, amr_fib_1_272_down + label_y_offset)

    if show_amr_fib_1_618
        // Recalculate the levels to ensure they're up to date
        amr_fib_1_618_up := true_month_open_price + (monthly_range_val * 1.618)
        amr_fib_1_618_down := true_month_open_price - (monthly_range_val * 1.618)

        line.set_x2(amr_fib_1_618_up_line, bar_index)
        line.set_y2(amr_fib_1_618_up_line, amr_fib_1_618_up)
        line.set_x2(amr_fib_1_618_down_line, bar_index)
        line.set_y2(amr_fib_1_618_down_line, amr_fib_1_618_down)

        if show_labels
            label.set_x(amr_fib_1_618_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_618_up_label, amr_fib_1_618_up + label_y_offset)
            label.set_x(amr_fib_1_618_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_618_down_label, amr_fib_1_618_down + label_y_offset)

// === TIMEFRAME RESTRICTION ===
// Only apply timeframe restriction if show_only_intraday is true and we're on a daily or higher timeframe
if show_only_intraday and timeframe.isdaily
    // Monthly levels
    line.delete(true_month_open_line)
    label.delete(true_month_open_label)
    line.delete(true_month_vertical)

    if show_full_amr
        line.delete(full_amr_up_line)
        line.delete(full_amr_down_line)
        label.delete(full_amr_up_label)
        label.delete(full_amr_down_label)

    if show_one_third_amr
        line.delete(one_third_amr_up_line)
        line.delete(one_third_amr_down_line)
        label.delete(one_third_amr_up_label)
        label.delete(one_third_amr_down_label)

    if show_two_third_amr
        line.delete(two_third_amr_up_line)
        line.delete(two_third_amr_down_line)
        label.delete(two_third_amr_up_label)
        label.delete(two_third_amr_down_label)

    if show_half_amr
        line.delete(half_amr_up_line)
        line.delete(half_amr_down_line)
        label.delete(half_amr_up_label)
        label.delete(half_amr_down_label)

    if show_amr_mult_1_5
        line.delete(amr_mult_1_5_up_line)
        line.delete(amr_mult_1_5_down_line)
        label.delete(amr_mult_1_5_up_label)
        label.delete(amr_mult_1_5_down_label)

    if show_amr_mult_2_0
        line.delete(amr_mult_2_0_up_line)
        line.delete(amr_mult_2_0_down_line)
        label.delete(amr_mult_2_0_up_label)
        label.delete(amr_mult_2_0_down_label)

    if show_amr_fib_1_272
        line.delete(amr_fib_1_272_up_line)
        line.delete(amr_fib_1_272_down_line)
        label.delete(amr_fib_1_272_up_label)
        label.delete(amr_fib_1_272_down_label)

    if show_amr_fib_1_618
        line.delete(amr_fib_1_618_up_line)
        line.delete(amr_fib_1_618_down_line)
        label.delete(amr_fib_1_618_up_label)
        label.delete(amr_fib_1_618_down_label)

    // Delete all monthly labels in arrays
    delete_all_labels(tmo_labels)
    delete_all_labels(full_amr_up_labels)
    delete_all_labels(full_amr_down_labels)
    delete_all_labels(one_third_amr_up_labels)
    delete_all_labels(one_third_amr_down_labels)
    delete_all_labels(two_third_amr_up_labels)
    delete_all_labels(two_third_amr_down_labels)
    delete_all_labels(half_amr_up_labels)
    delete_all_labels(half_amr_down_labels)
    delete_all_labels(amr_mult_1_5_up_labels)
    delete_all_labels(amr_mult_1_5_down_labels)
    delete_all_labels(amr_mult_2_0_up_labels)
    delete_all_labels(amr_mult_2_0_down_labels)
    delete_all_labels(amr_fib_1_272_up_labels)
    delete_all_labels(amr_fib_1_272_down_labels)
    delete_all_labels(amr_fib_1_618_up_labels)
    delete_all_labels(amr_fib_1_618_down_labels)

    true_month_line_active := false
