//@version=6
//@description The Open Range Breakout (ORB) Indicator with 50% extension levels, midpoint, and retracement-based signals. Identifies price breakouts from a daily opening range, plots high/low range lines with 50% extensions and midpoint, optional background fill, breakout signals with triangles after retracement, and triggers alerts. Ideal for intraday trading MNQ, MES, or other instruments.

indicator("Open Range Breakout (ORB) with 50% Extensions, Midpoint, and Alerts", overlay=true)

/// === USER INPUTS ===
rangeSession = input.session("0300-0315", title="Range Session (e.g., 09:30–09:45)", group="Session Settings")
timezone = input.string("America/New_York", title="Timezone", options=["America/New_York", "GMT+0"], group="Session Settings")
signalsUp = input.int(1, title="Number of Bullish Breakout Signals", minval=1, maxval=20, group="Breakout Settings")
signalsDn = input.int(1, title="Number of Bearish Breakout Signals", minval=1, maxval=20, group="Breakout Settings")
showRangeFill = input.bool(true, title="Show Background Fill?", group="Display Settings")
showSignals = input.bool(true, title="Show Breakout Signals?", group="Display Settings")
todayOnly = input.bool(true, title="Only Show Today's Range?", group="Display Settings")
rangeHighColor = input.color(color.green, title="Range High Line Color", group="Color Settings")
rangeLowColor = input.color(color.red, title="Range Low Line Color", group="Color Settings")
rangeMidColor = input.color(color.yellow, title="Range Midpoint Line Color", group="Color Settings")
rangeFillColor = input.color(color.teal, title="Background Fill Color", group="Color Settings")
extColor = input.color(color.blue, title="50% Extension Line Color", group="Color Settings")

/// === TIME CONTROLS ===
day_id = time("D", timezone)
isNewDay = day_id != day_id[1]
var float rangeHigh = na
var float rangeLow = na
var float rangeMid = na
var float extHigh = na
var float extLow = na
var bool rangeSet = false
var int currentDay = na
var int countUp = 0
var int countDn = 0
var bool touchedExtHigh = false
var bool touchedExtLow = false

inRangeSession = not na(time("", rangeSession, timezone))
currentDay := day_id

// Reset range, extensions, midpoint, breakout counts, and touch flags at new day
if isNewDay
    rangeHigh := na
    rangeLow := na
    rangeMid := na
    extHigh := na
    extLow := na
    rangeSet := false
    countUp := 0
    countDn := 0
    touchedExtHigh := false
    touchedExtLow := false

// Build range during session
if inRangeSession
    rangeHigh := na(rangeHigh) ? high : math.max(rangeHigh, high)
    rangeLow := na(rangeLow) ? low : math.min(rangeLow, low)
    rangeSet := false
else
    if not rangeSet and not na(rangeHigh) and not na(rangeLow)
        rangeSet := true
        // Calculate 50% extensions and midpoint
        float rangeSize = rangeHigh - rangeLow
        extHigh := rangeHigh + (rangeSize * 0.5)
        extLow := rangeLow - (rangeSize * 0.5)
        rangeMid := (rangeHigh + rangeLow) / 2

// Check if price touches 50% extension levels
if rangeSet
    if high >= extHigh
        touchedExtHigh := true
    if low <= extLow
        touchedExtLow := true

/// === BREAKOUT CONDITIONS WITH RETRACEMENT ===
breakoutUp = false
breakoutDn = false

// Bullish breakout: Check retracement to rangeHigh if extension was touched
if rangeSet and countUp < signalsUp
    if touchedExtHigh
        breakoutUp := close > rangeHigh and close[1] <= rangeHigh
    else
        breakoutUp := close > rangeHigh

// Bearish breakout: Check retracement to rangeLow if extension was touched
if rangeSet and countDn < signalsDn
    if touchedExtLow
        breakoutDn := close < rangeLow and close[1] >= rangeLow
    else
        breakoutDn := close < rangeLow

// Update breakout counts
if breakoutUp
    countUp := countUp + 1
if breakoutDn
    countDn := countDn + 1

/// === PLOT RANGE, MIDPOINT, AND EXTENSION LINES ===
plotRangeHigh = todayOnly and currentDay != day_id ? na : rangeHigh
plotRangeLow = todayOnly and currentDay != day_id ? na : rangeLow
plotRangeMid = todayOnly and currentDay != day_id ? na : rangeMid
plotExtHigh = todayOnly and currentDay != day_id ? na : extHigh
plotExtLow = todayOnly and currentDay != day_id ? na : extLow

plot(plotRangeHigh, title="Range High", color=rangeHighColor, linewidth=2, style=plot.style_linebr)
plot(plotRangeLow, title="Range Low", color=rangeLowColor, linewidth=2, style=plot.style_linebr)
plot(plotRangeMid, title="Range Midpoint", color=rangeMidColor, linewidth=1, style=plot.style_linebr)
plot(plotExtHigh, title="50% Extension High", color=extColor, linewidth=1, style=plot.style_linebr)
plot(plotExtLow, title="50% Extension Low", color=extColor, linewidth=1, style=plot.style_linebr)

/// === BACKGROUND FILL ===
fillColor = showRangeFill and rangeSet ? color.new(rangeFillColor, 90) : na
bg_fill = plot(showRangeFill ? rangeHigh : na, title="High Fill", display=display.none)
bg_base = plot(showRangeFill ? rangeLow : na, title="Low Fill", display=display.none)
fill(bg_fill, bg_base, color=fillColor)

/// === BREAKOUT SIGNAL SHAPES ===
plotshape(showSignals and breakoutUp, location=location.belowbar, color=color.green, style=shape.triangleup, size=size.small, title="Breakout Up")
plotshape(showSignals and breakoutDn, location=location.abovebar, color=color.red, style=shape.triangledown, size=size.small, title="Breakout Down")

/// === ALERT CONDITIONS ===
alertcondition(breakoutUp, title="ORB Breakout Up", message="Bullish Breakout above Open Range on {{ticker}}!")
alertcondition(breakoutDn, title="ORB Breakout Down", message="Bearish Breakout below Open Range on {{ticker}}!")