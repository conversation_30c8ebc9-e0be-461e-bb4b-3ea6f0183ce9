// //@version=5
// indicator("Relative Volume Candle Coloring", overlay = true)

// Input Groups
g1 = 'General'
g2 = 'Volume Thresholds'
g5 = 'Additional Settings'

// General
upcol    = input.color(#d1d4dc, 'Primary Colors              ▲', inline = 'udcol', group = g1)
downcol  = input.color(#9598a1f6, '▼', inline = 'udcol', group = g1)

// Volume Thresholds
show_mult1     = input.bool(true, '',  inline = 'mult1', group = g2)
mult           = input.float(1.5, '>', inline = 'mult1', group = g2, step = 0.1)
upcol_mult1    = input.color(#c8e6c9, '     ▲', inline = 'mult1', group = g2)
downcol_mult1  = input.color(#faa1a4, '▼', inline = 'mult1', group = g2)

show_mult2     = input.bool(true, '', inline = 'mult2', group = g2)
mult2          = input.float(2.5, '>', inline = 'mult2', group = g2, step = 0.1)
upcol_mult2    = input.color(#a5d6a7, '     ▲', inline = 'mult2', group = g2)
downcol_mult2  = input.color(#f77c80, '▼', inline = 'mult2', group = g2)

show_mult3     = input.bool(true, '', inline = 'mult3', group = g2)
mult3          = input.float(3.5, '>', inline = 'mult3', group = g2, step = 0.1)
upcol_mult3    = input.color(#66bb6a, '     ▲', inline = 'mult3', group = g2)
downcol_mult3  = input.color(#f7525f, '▼', inline = 'mult3', group = g2)

// Additional settings
gradient      = input.bool(false, 'Gradient Coloring', inline = 'g', group = g5)
gr1           = input.color(#f7525f, '', inline = 'g', group = g5)
gr2           = input.color(#ffe0b2, '', inline = 'g', group = g5)
rellen        = input.int(20, 'Relative Length', group = g5)
color_candles = input.bool(true, 'Enable Candle Coloring', group = g5)

// Relative function
f_relative(src) =>
    src / ta.sma(src, rellen)

// Relative Volume calculations
rvol = f_relative(volume)

// Coloring
CRV_COL       = close > open ? upcol : downcol
color bar_col = color.rgb(0,0,0,100)

// Threshold coloring logic
if show_mult1 and rvol >= mult
    CRV_COL := close>open ? upcol_mult1 : downcol_mult1
    bar_col := CRV_COL
if show_mult2 and rvol >= mult2
    CRV_COL := close>open ? upcol_mult2 : downcol_mult2
    bar_col := CRV_COL
if show_mult3 and rvol >= mult3
    CRV_COL := close>open ? upcol_mult3 : downcol_mult3
    bar_col := CRV_COL

if gradient
    glen = 50
    CRV_COL := color.from_gradient(rvol, 0, ta.highest(rvol, glen), gr2, gr1)

// Coloring candles
barcolor(color_candles ? bar_col : na)
