// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Dev Lucem

//@version=5
//@author=devlucem

//
//       THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
//       THE <PERSON><PERSON><PERSON><PERSON> SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
//       I HOPE U LOVE IT
//


////////
// Seek Menu
import DevLucem/ZigLib/1 as ZigZag
indicator('ZigZag++', 'ZigZag++ [LD]', true, format.price, max_labels_count=200, max_lines_count=50)

////////
// Fetch Ingredients 
// [
Depth = input.int(12, 'Depth', minval=1, step=1, group="ZigZag Config")
Deviation = input.int(5, 'Deviation', minval=1, step=1, group="ZigZag Config")
Backstep = input.int(2, 'Backstep', minval=2, step=1, group="ZigZag Config")
line_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group="Lines")
labels = input(0, "Labels Transparency", group="Labels")
upcolor = input(color.lime, 'Bull Color', group="Colors")
dncolor = input(color.red, 'Bear Color', group="Colors")
lines = input(0, "Lines Transparency", group="Lines")
background = input(80, "Background Transparency", group="Colors")
label_size = switch input.int(3, "Label SIze", minval=1, maxval=5, group="Labels")
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
repaint = input(true, 'Repaint Levels')
extend = input(false, "Extend ZigZag", group="Lines")
// ]


// ////////
// // Bake it with a simple oven this time
[direction, z1, z2] = ZigZag.zigzag(low, high, Depth, Deviation, Backstep)
string nowPoint = ""
var float lastPoint = z1.price[1]
if bool(ta.change(direction))
    lastPoint := z1.price[1]


// ////////
// // Let it Cool And Serve
line zz = na
label point = na

if repaint
    zz := line.new(z1, z2, xloc.bar_time, extend? extend.right: extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
    nowPoint := direction<0? (z2.price<lastPoint? "LL": "HL"): (z2.price>lastPoint? "HH": "LH")
    point := label.new(z2, nowPoint, xloc.bar_time, yloc.price, 
     color.new(direction<0? upcolor: dncolor, labels), direction>0? label.style_label_down: label.style_label_up, color.new(direction>0? upcolor: dncolor, labels), label_size)
    if direction == direction[1]
        line.delete(zz[1])
        label.delete(point[1])
    else
        line.set_extend(zz[1], extend.none)
else
    if direction != direction[1]
        zz := line.new(z1[1], z2[1], xloc.bar_time, extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
        nowPoint := direction[1]<0? (z2.price[1]<lastPoint[1]? "LL": "HL"): (z2.price[1]>lastPoint[1]? "HH": "LH")
        point := label.new(z2[1], nowPoint, xloc.bar_time, yloc.price, 
         color.new(direction[1]<0? upcolor: dncolor, labels), direction[1]>0? label.style_label_down: label.style_label_up, color.new(direction[1]>0? upcolor: dncolor, labels), label_size)
bgcolor(direction<0? color.new(dncolor, background): color.new(upcolor, background), title='Direction Background')
plotarrow(direction, "direction", display=display.status_line)


// ////////
// // Declare Meal Was Sweet By Force
alertcondition(nowPoint == "HH" and z2.price != z2.price[1], "New Higher High", 'Zigzag on {{ticker}} higher higher high detected at {{time}}')
alertcondition(nowPoint == "LH" and z2.price != z2.price[1], "New Lower High", 'Zigzag on {{ticker}} higher lower high detected at {{time}}')
alertcondition(nowPoint == "HL" and z2.price != z2.price[1], "New Higher Low", 'Zigzag on {{ticker}} higher lower low detected at {{time}}')
alertcondition(nowPoint == "LL" and z2.price != z2.price[1], "New Lower Low", 'Zigzag on {{ticker}} lower low detected at {{time}}')
alertcondition(direction != direction[1], 'Direction Changed', 'Zigzag on {{ticker}} direction changed at {{time}}')
alertcondition(direction != direction[1] and direction>0, 'Bullish Direction', 'Zigzag on {{ticker}} bullish direction at {{time}}')
alertcondition(direction != direction[1] and direction<0, 'Bearish Direction', 'Zigzag on {{ticker}} bearish direction at {{time}}')

if direction != direction[1]
    alert((direction<0? "Bearish": "Bullish") + " Direction Final ", alert.freq_once_per_bar_close)
// //@version=5
// indicator("Relative Volume Candle Coloring", overlay = true)

// Input Groups
g1 = 'General'
g2 = 'Volume Thresholds'
g5 = 'Additional Settings'

// General
upcol    = input.color(#d1d4dc, 'Primary Colors              ▲', inline = 'udcol', group = g1)
downcol  = input.color(#9598a1f6, '▼', inline = 'udcol', group = g1)

// Volume Thresholds
show_mult1     = input.bool(true, '',  inline = 'mult1', group = g2)
mult           = input.float(1.5, '>', inline = 'mult1', group = g2, step = 0.1)
upcol_mult1    = input.color(#c8e6c9, '     ▲', inline = 'mult1', group = g2)
downcol_mult1  = input.color(#faa1a4, '▼', inline = 'mult1', group = g2)

show_mult2     = input.bool(true, '', inline = 'mult2', group = g2)
mult2          = input.float(2.5, '>', inline = 'mult2', group = g2, step = 0.1)
upcol_mult2    = input.color(#a5d6a7, '     ▲', inline = 'mult2', group = g2)
downcol_mult2  = input.color(#f77c80, '▼', inline = 'mult2', group = g2)

show_mult3     = input.bool(true, '', inline = 'mult3', group = g2)
mult3          = input.float(3.5, '>', inline = 'mult3', group = g2, step = 0.1)
upcol_mult3    = input.color(#66bb6a, '     ▲', inline = 'mult3', group = g2)
downcol_mult3  = input.color(#f7525f, '▼', inline = 'mult3', group = g2)

// Additional settings
gradient      = input.bool(false, 'Gradient Coloring', inline = 'g', group = g5)
gr1           = input.color(#f7525f, '', inline = 'g', group = g5)
gr2           = input.color(#ffe0b2, '', inline = 'g', group = g5)
rellen        = input.int(20, 'Relative Length', group = g5)
color_candles = input.bool(true, 'Enable Candle Coloring', group = g5)

// Relative function
f_relative(src) =>
    src / ta.sma(src, rellen)

// Relative Volume calculations
rvol = f_relative(volume)

// Coloring
CRV_COL       = close > open ? upcol : downcol
color bar_col = color.rgb(0,0,0,100)

// Threshold coloring logic
if show_mult1 and rvol >= mult
    CRV_COL := close>open ? upcol_mult1 : downcol_mult1
    bar_col := CRV_COL
if show_mult2 and rvol >= mult2
    CRV_COL := close>open ? upcol_mult2 : downcol_mult2
    bar_col := CRV_COL
if show_mult3 and rvol >= mult3
    CRV_COL := close>open ? upcol_mult3 : downcol_mult3
    bar_col := CRV_COL

if gradient
    glen = 50
    CRV_COL := color.from_gradient(rvol, 0, ta.highest(rvol, glen), gr2, gr1)

// Coloring candles
barcolor(color_candles ? bar_col : na)